/**
 * Advanced Humanization Engine - Designed to achieve ≤20% AI detection
 * Implements sophisticated algorithms based on modern AI detection research
 * Enhanced with smart content analysis and formatting preservation
 */

import {
    analyzeDocumentStructure,
    identifyProtectedSentences,
    calculateHesitationFrequency
} from './contentAnalyzer.js';

/**
 * Main advanced humanization function
 * Applies multiple sophisticated techniques to bypass AI detection
 */
export function advancedHumanization(text, options = {}) {
    const {
        aggressiveness = 0.7, // 0-1 scale, higher = more changes
        maintainTone = true
    } = options;

    // Detect high AI patterns and auto-adjust aggressiveness
    const adjustedAggressiveness = detectAndAdjustAggressiveness(text, aggressiveness);

    const originalText = text;
    let result = text;

    console.log('Starting advanced humanization...');

    // Phase 1: Smart Content Analysis and Protection
    const documentAnalysis = analyzeDocumentStructure(result);
    const hesitationFrequency = calculateHesitationFrequency(documentAnalysis);

    // Determine processing approach based on content type and AI detection level
    const hasHighAIDetection = adjustedAggressiveness > 0.9;
    const hasFormalElements = documentAnalysis.formalElements && documentAnalysis.formalElements.length > 0;

    // For high AI detection content, use aggressive processing even if formatted
    // Only use conservative processing for content with formal elements (headings, lists, etc.)
    if (documentAnalysis.preserveFormatting && hasFormalElements && !hasHighAIDetection) {
        result = processFormattedDocument(originalText, adjustedAggressiveness, hesitationFrequency, documentAnalysis);
    } else {
        // For regular text or high AI detection content, use aggressive sentence-based processing
        const protectedSentences = identifyProtectedSentences(result, documentAnalysis);
        const textAnalysis = analyzeTextStructure(result);

        // Phase 2: Advanced Sentence Restructuring (most important for detection bypass)
        result = advancedSentenceRestructuring(result, textAnalysis, adjustedAggressiveness, protectedSentences);

        // Phase 3: Context-Aware Synonym Replacement
        result = contextAwareSynonymReplacement(result, adjustedAggressiveness, protectedSentences);

        // Phase 4: Perplexity and Burstiness Enhancement
        result = enhancePerplexityAndBurstiness(result, textAnalysis, adjustedAggressiveness, protectedSentences);

        // Phase 5: Human Writing Pattern Injection (with reduced frequency)
        result = injectHumanWritingPatterns(result, adjustedAggressiveness, hesitationFrequency, protectedSentences);

        // Phase 6: Semantic Coherence Disruption (subtle)
        result = subtleCoherenceDisruption(result, adjustedAggressiveness, protectedSentences);

        // Preserve paragraph structure for formatted content
        if (documentAnalysis.preserveFormatting) {
            result = preserveParagraphStructure(originalText, result);
        }
    }

    // Final Polish and Quality Check
    result = finalPolishAndQualityCheck(result, maintainTone, documentAnalysis);

    console.log('Advanced humanization completed');
    return result;
}

/**
 * Processes formatted documents while preserving structure
 */
function processFormattedDocument(text, aggressiveness, hesitationFrequency, documentAnalysis) {
    const lines = text.split('\n');
    const processedLines = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        // Preserve empty lines exactly as they are
        if (!trimmedLine) {
            processedLines.push(line); // This preserves the original whitespace/empty line
            continue;
        }

        // Check if this line should be protected
        if (shouldProtectLine(trimmedLine, documentAnalysis)) {
            processedLines.push(line);
            continue;
        }

        // Process the line content while preserving indentation
        const indentation = line.match(/^\s*/)[0];
        let processedContent = trimmedLine;

        // Apply conservative modifications to non-protected lines
        if (trimmedLine.length > 20) {
            // Apply only safe modifications that don't break structure
            processedContent = applySafeModifications(processedContent, aggressiveness, hesitationFrequency);
        }

        processedLines.push(indentation + processedContent);
    }

    return processedLines.join('\n');
}

/**
 * Checks if a line should be protected from modification
 */
function shouldProtectLine(line, _documentAnalysis) {
    // Protect headings, section numbers, and formal markers
    const protectionPatterns = [
        /^[IVX]+\./,                    // Roman numerals
        /^[A-Z]\./,                     // Letter markers
        /^\d+\./,                       // Number markers
        /^\d+\.\d+/,                    // Decimal numbering
        /^[A-Z][A-Z\s]+:?\s*$/,        // ALL CAPS headings
        /^(Introduction|Conclusion|Summary|Abstract|Background|Methodology|Results):/i,
        /^(Hook|Thesis|Topic|Executive Summary|Key Findings|Strategic Recommendations):/i,
        /^(Step|Phase|Part)\s+\d+/i,   // Step/Phase markers
        /^[-•*]\s/,                     // Bullet points
        /^[a-z]\)\s/,                   // Lettered lists
        /^\d+\)\s/,                     // Numbered lists with parentheses
        /:\s*$/,                        // Lines ending with colon
        /^-\s/                          // Dash lists
    ];

    return protectionPatterns.some(pattern => pattern.test(line));
}

/**
 * Applies safe modifications that don't break document structure
 */
function applySafeModifications(text, aggressiveness, hesitationFrequency) {
    let result = text;

    // Only apply very conservative word replacements
    const safeReplacements = {
        'utilize': 'use',
        'implement': 'put in place',
        'demonstrate': 'show',
        'facilitate': 'help',
        'optimize': 'improve'
    };

    Object.entries(safeReplacements).forEach(([formal, casual]) => {
        if (Math.random() < aggressiveness * 0.3) {
            const regex = new RegExp(`\\b${formal}\\b`, 'gi');
            result = result.replace(regex, casual);
        }
    });

    // Very rarely add hesitation (only for long sentences)
    if (text.length > 50 && Math.random() < hesitationFrequency * 0.5) {
        const hesitations = ['Actually, ', 'Well, '];
        const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];
        result = hesitation + result.charAt(0).toLowerCase() + result.slice(1);
    }

    return result;
}

/**
 * Analyzes text structure to inform humanization decisions
 */
function analyzeTextStructure(text) {
    const sentences = text.split(/(?<=[.!?])\s+/);
    const words = text.split(/\s+/);
    
    const analysis = {
        sentenceCount: sentences.length,
        averageSentenceLength: sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length,
        wordCount: words.length,
        averageWordLength: words.reduce((sum, w) => sum + w.length, 0) / words.length,
        sentenceLengths: sentences.map(s => s.length),
        complexSentenceRatio: sentences.filter(s => s.includes(',') || s.includes(';')).length / sentences.length,
        formalWordCount: countFormalWords(text),
        aiTriggerCount: countAITriggers(text)
    };
    
    // Calculate burstiness (variation in sentence length)
    const lengths = analysis.sentenceLengths;
    const mean = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const variance = lengths.reduce((sum, len) => sum + Math.pow(len - mean, 2), 0) / lengths.length;
    analysis.burstiness = Math.sqrt(variance) / mean;
    
    return analysis;
}

/**
 * Advanced sentence restructuring - the most critical component
 */
function advancedSentenceRestructuring(text, _analysis, aggressiveness, protectedSentences = []) {
    let result = text;

    // Check if text has line breaks that should be preserved
    const hasLineBreaks = text.includes('\n');

    if (hasLineBreaks) {
        // Process line by line to preserve formatting
        return processTextWithLineBreaks(text, (sentence) =>
            processSingleSentenceRestructuring(sentence, aggressiveness, protectedSentences)
        );
    }

    const sentences = result.split(/(?<=[.!?])\s+/);

    const restructuredSentences = sentences.map((sentence, index) => {
        // Skip protected sentences (headings, formal content, etc.)
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip very short sentences
        if (sentence.length < 20) return sentence;

        let newSentence = sentence;

        // 1. Clause reordering (high impact on AI detection)
        if (Math.random() < aggressiveness * 0.4) {
            newSentence = reorderClauses(newSentence);
        }

        // 2. Sentence splitting/combining based on human patterns
        if (Math.random() < aggressiveness * 0.3) {
            if (sentence.length > 80 && sentence.includes(',')) {
                // Split long sentences
                const splitResult = splitSentenceNaturally(newSentence);
                if (splitResult.length > 1) {
                    return splitResult.join(' ');
                }
            }
        }

        // 3. Passive/Active voice variation (humans mix both)
        if (Math.random() < aggressiveness * 0.25) {
            newSentence = varyVoiceNaturally(newSentence);
        }

        // 4. Sentence starter variation
        if (Math.random() < aggressiveness * 0.35) {
            newSentence = varyStarterPhrases(newSentence);
        }

        return newSentence;
    });

    return restructuredSentences.join(' ');
}

/**
 * Reorders clauses within sentences to create more natural flow
 */
function reorderClauses(sentence) {
    // Handle sentences with dependent clauses
    const patterns = [
        // "Because X, Y" -> "Y because X"
        {
            regex: /^(Because|Since|As|When|While|If|Although|Though)\s+([^,]+),\s*(.+)$/i,
            reorder: (match, connector, clause1, clause2) => {
                if (Math.random() > 0.5) {
                    return `${clause2}, ${connector.toLowerCase()} ${clause1}`;
                }
                return match;
            }
        },
        // "X, which Y, Z" -> "X Z, which Y" or other variations
        {
            regex: /^([^,]+),\s*which\s+([^,]+),\s*(.+)$/i,
            reorder: (match, main, which, rest) => {
                const variations = [
                    `${main} ${rest}, which ${which}`,
                    `${main}, and it ${which}, ${rest}`,
                    match // Keep original sometimes
                ];
                return variations[Math.floor(Math.random() * variations.length)];
            }
        }
    ];
    
    for (const pattern of patterns) {
        const match = sentence.match(pattern.regex);
        if (match) {
            return pattern.reorder(...match);
        }
    }
    
    return sentence;
}

/**
 * Splits long sentences naturally at appropriate points
 */
function splitSentenceNaturally(sentence) {
    const splitPoints = [
        { regex: /,\s*(and|but|or|so)\s+/, replacement: '. ' },
        { regex: /;\s*/, replacement: '. ' },
        { regex: /,\s*which\s+/, replacement: '. This ' },
        { regex: /,\s*because\s+/, replacement: '. This is because ' }
    ];
    
    for (const point of splitPoints) {
        if (sentence.match(point.regex)) {
            const parts = sentence.split(point.regex);
            if (parts.length >= 2 && parts[0].length > 30 && parts[1].length > 20) {
                const firstPart = parts[0].trim() + '.';
                const secondPart = parts.slice(1).join(' ').trim();
                const capitalizedSecond = secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                return [firstPart, capitalizedSecond];
            }
        }
    }
    
    return [sentence];
}

/**
 * Varies voice (active/passive) naturally
 */
function varyVoiceNaturally(sentence) {
    // Simple passive to active conversion
    const passivePattern = /(\w+)\s+(?:is|was|are|were)\s+(\w+ed)\s+by\s+(\w+)/i;
    const passiveMatch = sentence.match(passivePattern);
    
    if (passiveMatch && Math.random() > 0.6) {
        const [full, object, verb, subject] = passiveMatch;
        const activeForm = verb.replace(/ed$/, ''); // Simplified
        return sentence.replace(full, `${subject} ${activeForm} ${object}`);
    }
    
    return sentence;
}

/**
 * Varies sentence starters to break AI patterns
 */
function varyStarterPhrases(sentence) {
    const starters = [
        { pattern: /^However,\s*/i, replacements: ['But ', 'Still, ', 'Yet '] },
        { pattern: /^Therefore,\s*/i, replacements: ['So ', 'Thus, ', 'Hence '] },
        { pattern: /^Furthermore,\s*/i, replacements: ['Also, ', 'Plus, ', 'And '] },
        { pattern: /^Moreover,\s*/i, replacements: ['Also, ', 'Plus, ', 'What\'s more, '] },
        { pattern: /^Additionally,\s*/i, replacements: ['Also, ', 'Plus, ', 'On top of that, '] },
        { pattern: /^In addition,\s*/i, replacements: ['Also, ', 'Plus, ', 'Besides, '] }
    ];
    
    for (const starter of starters) {
        if (sentence.match(starter.pattern)) {
            const replacement = starter.replacements[Math.floor(Math.random() * starter.replacements.length)];
            return sentence.replace(starter.pattern, replacement);
        }
    }
    
    return sentence;
}

/**
 * Context-aware synonym replacement using semantic understanding
 */
function contextAwareSynonymReplacement(text, aggressiveness, protectedSentences = []) {
    let result = text;

    // Check if text has line breaks that should be preserved
    const hasLineBreaks = text.includes('\n');

    if (hasLineBreaks) {
        // Process line by line to preserve formatting
        return processTextWithLineBreaks(text, (sentence) =>
            processSingleSentenceSynonyms(sentence, aggressiveness, protectedSentences)
        );
    }

    const sentences = result.split(/(?<=[.!?])\s+/);
    
    // Advanced synonym groups with context awareness
    const contextualSynonyms = {
        // Verbs - context matters greatly
        'utilize': { 
            formal: ['employ', 'apply'], 
            casual: ['use', 'try'], 
            context: 'tool|method|approach' 
        },
        'demonstrate': { 
            formal: ['illustrate', 'exhibit'], 
            casual: ['show', 'prove'], 
            context: 'example|evidence|proof' 
        },
        'implement': { 
            formal: ['execute', 'deploy'], 
            casual: ['do', 'put in place', 'set up'], 
            context: 'plan|strategy|solution' 
        },
        // Adjectives - tone-sensitive
        'significant': { 
            formal: ['substantial', 'considerable'], 
            casual: ['big', 'major', 'important'], 
            context: 'impact|change|difference' 
        },
        'comprehensive': { 
            formal: ['thorough', 'extensive'], 
            casual: ['complete', 'full', 'detailed'], 
            context: 'analysis|study|review' 
        }
    };
    
    // Process each sentence, skipping protected ones
    const processedSentences = sentences.map((sentence, sentenceIndex) => {
        if (protectedSentences.includes(sentenceIndex)) {
            return sentence;
        }

        // Process each word with context
        const words = sentence.split(/(\s+)/);
        const processedWords = words.map((word, index) => {
            const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');

            if (contextualSynonyms[cleanWord] && Math.random() < aggressiveness * 0.4) {
                const synonymGroup = contextualSynonyms[cleanWord];
                const context = getWordContext(words, index, 3);

                // Choose synonym based on context
                let synonyms;
                if (synonymGroup.context && context.includes(synonymGroup.context)) {
                    synonyms = [...synonymGroup.formal, ...synonymGroup.casual];
                } else if (context.includes('formal') || context.includes('academic')) {
                    synonyms = synonymGroup.formal;
                } else {
                    synonyms = synonymGroup.casual;
                }

                if (synonyms.length > 0) {
                    const chosen = synonyms[Math.floor(Math.random() * synonyms.length)];
                    return preserveCase(word, chosen);
                }
            }

            return word;
        });

        return processedWords.join('');
    });

    return processedSentences.join(' ');
}

/**
 * Gets context around a word for better synonym selection
 */
function getWordContext(words, index, radius) {
    const start = Math.max(0, index - radius);
    const end = Math.min(words.length, index + radius + 1);
    return words.slice(start, end).join(' ').toLowerCase();
}

/**
 * Preserves original word casing when replacing
 */
function preserveCase(original, replacement) {
    if (!original || !replacement) return replacement;
    
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }
    if (original === original.toLowerCase()) {
        return replacement.toLowerCase();
    }
    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }
    return replacement;
}

/**
 * Counts formal words that AI tends to overuse
 */
function countFormalWords(text) {
    const formalWords = [
        'utilize', 'implement', 'demonstrate', 'facilitate', 'optimize',
        'comprehensive', 'significant', 'substantial', 'furthermore', 'moreover'
    ];
    
    let count = 0;
    const lowerText = text.toLowerCase();
    formalWords.forEach(word => {
        const regex = new RegExp(`\\b${word}\\b`, 'g');
        const matches = lowerText.match(regex);
        if (matches) count += matches.length;
    });
    
    return count;
}

/**
 * Counts AI trigger phrases
 */
function countAITriggers(text) {
    const triggers = [
        'it is important to note', 'it should be noted', 'in conclusion',
        'to summarize', 'furthermore', 'moreover', 'additionally'
    ];

    let count = 0;
    const lowerText = text.toLowerCase();
    triggers.forEach(trigger => {
        if (lowerText.includes(trigger)) count++;
    });

    return count;
}

/**
 * Enhances perplexity and burstiness - critical for bypassing AI detection
 */
function enhancePerplexityAndBurstiness(text, analysis, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    // Calculate target burstiness (humans have higher variation)
    const targetBurstiness = Math.max(0.4, analysis.burstiness || 0.3 * 1.5);

    // Use target burstiness to vary sentence processing
    const useBurstiness = targetBurstiness > 0.5;

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        let newSentence = sentence;

        // 1. Vary sentence complexity dramatically
        // Use burstiness to vary processing intensity
        const processingProbability = useBurstiness ? aggressiveness * 0.6 : aggressiveness * 0.5;
        if (Math.random() < processingProbability) {
            if (sentence.length > 60) {
                // Make some sentences much simpler
                newSentence = simplifyComplexSentence(newSentence);
            } else if (sentence.length < 30) {
                // Make some sentences more complex
                newSentence = complexifySimpleSentence(newSentence);
            }
        }

        // 2. Add unexpected word choices (increase perplexity)
        if (Math.random() < aggressiveness * 0.3) {
            newSentence = addUnexpectedWordChoices(newSentence);
        }

        // 3. Inject human-like redundancy and clarification
        if (Math.random() < aggressiveness * 0.2) {
            newSentence = addHumanRedundancy(newSentence);
        }

        return newSentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Simplifies complex sentences to increase burstiness
 */
function simplifyComplexSentence(sentence) {
    // Remove unnecessary qualifiers
    let simplified = sentence
        .replace(/\b(very|quite|rather|somewhat|fairly|pretty)\s+/gi, '')
        .replace(/\b(in order to|so as to)\b/gi, 'to')
        .replace(/\b(due to the fact that|owing to the fact that)\b/gi, 'because')
        .replace(/\b(in spite of the fact that|despite the fact that)\b/gi, 'although');

    // Break at natural points
    if (simplified.includes(' and ') && simplified.length > 80) {
        const parts = simplified.split(' and ');
        if (parts.length === 2 && parts[0].length > 20 && parts[1].length > 20) {
            return parts[0].trim() + '. ' + parts[1].charAt(0).toUpperCase() + parts[1].slice(1);
        }
    }

    return simplified;
}

/**
 * Adds complexity to simple sentences
 */
function complexifySimpleSentence(sentence) {
    const complexifiers = [
        { pattern: /^(\w+\s+\w+)\s+(\w+)/, replacement: '$1 actually $2' },
        { pattern: /(\w+)\s+(is|are|was|were)\s+(\w+)/, replacement: '$1 $2 really $3' },
        { pattern: /\.$/, replacement: ', which is interesting.' }
    ];

    for (const complexifier of complexifiers) {
        if (sentence.match(complexifier.pattern) && Math.random() > 0.7) {
            return sentence.replace(complexifier.pattern, complexifier.replacement);
        }
    }

    return sentence;
}

/**
 * Adds unexpected word choices to increase perplexity
 */
function addUnexpectedWordChoices(sentence) {
    const unexpectedReplacements = {
        'good': ['solid', 'decent', 'fine', 'okay'],
        'bad': ['rough', 'tough', 'tricky', 'messy'],
        'big': ['huge', 'massive', 'enormous', 'giant'],
        'small': ['tiny', 'little', 'mini', 'compact'],
        'fast': ['quick', 'speedy', 'rapid', 'swift'],
        'slow': ['sluggish', 'gradual', 'steady', 'leisurely']
    };

    let result = sentence;
    Object.entries(unexpectedReplacements).forEach(([common, alternatives]) => {
        const regex = new RegExp(`\\b${common}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.7) {
                const alternative = alternatives[Math.floor(Math.random() * alternatives.length)];
                return preserveCase(match, alternative);
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds human-like redundancy and clarification
 */
function addHumanRedundancy(sentence) {
    const redundancyPatterns = [
        { pattern: /\b(important|significant|crucial)\b/gi, addition: ' (really $1)' },
        { pattern: /\b(easy|simple|straightforward)\b/gi, addition: ' (pretty $1)' },
        { pattern: /\b(difficult|hard|challenging)\b/gi, addition: ' (quite $1)' }
    ];

    for (const pattern of redundancyPatterns) {
        if (sentence.match(pattern.pattern) && Math.random() > 0.8) {
            return sentence.replace(pattern.pattern, (match) => {
                return match + pattern.addition.replace('$1', match.toLowerCase());
            });
        }
    }

    return sentence;
}

/**
 * Injects human writing patterns that AI rarely exhibits
 */
function injectHumanWritingPatterns(text, aggressiveness, hesitationFrequency, protectedSentences = []) {
    let result = text;

    // 1. Add human hesitation and self-correction (with controlled frequency)
    result = addHesitationPatterns(result, aggressiveness, hesitationFrequency, protectedSentences);

    // 2. Inject conversational elements (reduced frequency)
    result = addConversationalElements(result, aggressiveness * 0.5, protectedSentences);

    // 3. Add human-like tangents and asides (very reduced frequency)
    result = addHumanTangents(result, aggressiveness * 0.3, protectedSentences);

    // 4. Inject personal perspective markers (reduced frequency)
    result = addPersonalPerspective(result, aggressiveness * 0.4, protectedSentences);

    return result;
}

/**
 * Enhanced hesitation patterns with contextual intelligence
 */
function addHesitationPatterns(text, _aggressiveness, hesitationFrequency, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const hesitationMarkers = [
        'Actually,', 'Listen,', 'Well,', 'You know,', 'I mean,',
        'To be honest,', 'Frankly,', 'Look,', 'Honestly,', 'So,'
    ];

    const contextualAlternatives = {
        'transition': ['However,', 'Meanwhile,', 'Furthermore,', 'Additionally,'],
        'emphasis': ['Notably,', 'Importantly,', 'Significantly,', 'Remarkably,'],
        'clarification': ['In other words,', 'That is to say,', 'Specifically,', 'Namely,']
    };

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences (headings, formal content, etc.)
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip sentences that look like formal content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Calculate probability based on context
        const probability = calculateHesitationProbability(sentence, hesitationFrequency);

        // Only add hesitation if probability check passes
        if (Math.random() < probability) {
            return addContextualHesitation(sentence, contextualAlternatives, hesitationMarkers);
        }

        // Apply other humanization techniques without hesitation markers
        return applySubtleHumanization(sentence);
    });

    return enhancedSentences.join(' ');
}

/**
 * Apply subtle humanization techniques without hesitation markers
 */
function applySubtleHumanization(sentence) {
    let processed = sentence;

    // Subtle word variations
    processed = applyWordVariations(processed);

    // Slight structural adjustments
    processed = applyStructuralVariations(processed);

    return processed;
}

/**
 * Process text while preserving line breaks
 */
function processTextWithLineBreaks(text, processingFunction) {
    const lines = text.split('\n');

    return lines.map(line => {
        const trimmedLine = line.trim();

        // Skip empty lines or lines that should be protected
        if (!trimmedLine || isInappropriateForHesitation(trimmedLine)) {
            return line;
        }

        // Process the line content while preserving indentation
        const indentation = line.match(/^\s*/)[0];
        const processedContent = processingFunction(trimmedLine);

        return indentation + processedContent;
    }).join('\n');
}

/**
 * Process a single sentence for restructuring
 */
function processSingleSentenceRestructuring(sentence, aggressiveness, _protectedSentences) {
    if (!sentence.trim() || isInappropriateForHesitation(sentence)) {
        return sentence;
    }

    let newSentence = sentence;

    // Apply restructuring techniques
    if (Math.random() < aggressiveness * 0.4) {
        // 1. Clause reordering
        newSentence = reorderClauses(newSentence);
    }

    if (Math.random() < aggressiveness * 0.3) {
        if (sentence.length > 80 && sentence.includes(',')) {
            // Split long sentences
            const splitResult = splitSentenceNaturally(newSentence);
            if (splitResult.length > 1) {
                return splitResult.join(' ');
            }
        }
    }

    return newSentence;
}

/**
 * Process a single sentence for synonym replacement
 */
function processSingleSentenceSynonyms(sentence, aggressiveness, _protectedSentences) {
    if (!sentence.trim() || isInappropriateForHesitation(sentence)) {
        return sentence;
    }

    // Apply basic synonym replacement logic here
    // This is a simplified version - you can expand this with the full synonym logic
    let result = sentence;

    // Basic word variations
    const variations = {
        'very': ['quite', 'rather', 'pretty', 'fairly'],
        'also': ['additionally', 'furthermore', 'moreover', 'as well'],
        'but': ['however', 'yet', 'although', 'though'],
        'because': ['since', 'as', 'due to the fact that'],
        'shows': ['demonstrates', 'indicates', 'reveals', 'suggests']
    };

    for (const [original, alternatives] of Object.entries(variations)) {
        if (Math.random() < aggressiveness * 0.3) {
            const regex = new RegExp(`\\b${original}\\b`, 'gi');
            const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
            result = result.replace(regex, replacement);
        }
    }

    return result;
}

/**
 * Enhanced word variations with aggressive AI-detection bypass patterns
 */
function applyWordVariations(sentence) {
    // Comprehensive AI-typical word replacements
    const aggressiveVariations = {
        // AI loves these formal words - replace aggressively
        'artificial intelligence': ['AI technology', 'machine learning systems', 'smart technology', 'automated systems'],
        'represents': ['is', 'acts as', 'serves as', 'functions as'],
        'transformative': ['game-changing', 'revolutionary', 'groundbreaking', 'innovative'],
        'fundamentally': ['completely', 'totally', 'entirely', 'thoroughly'],
        'revolutionizes': ['changes', 'transforms', 'reshapes', 'modernizes'],
        'organizations': ['companies', 'businesses', 'firms', 'enterprises'],
        'increasingly': ['more and more', 'progressively', 'gradually', 'steadily'],
        'implementing': ['using', 'adopting', 'deploying', 'putting in place'],
        'optimize': ['improve', 'enhance', 'boost', 'maximize'],
        'operational efficiency': ['performance', 'productivity', 'effectiveness', 'workflow'],
        'enhance': ['improve', 'boost', 'strengthen', 'upgrade'],
        'competitive advantages': ['edge over competitors', 'market benefits', 'business benefits', 'strategic benefits'],
        'comprehensive': ['complete', 'thorough', 'full', 'extensive'],
        'strategic planning': ['planning', 'strategy development', 'business planning', 'preparation'],
        'substantial': ['significant', 'considerable', 'major', 'large'],
        'financial investment': ['investment', 'funding', 'capital', 'money'],
        'numerous studies': ['many studies', 'research', 'multiple reports', 'various analyses'],
        'demonstrate': ['show', 'prove', 'indicate', 'reveal'],
        'utilizing': ['using', 'employing', 'applying', 'working with'],
        'experience': ['see', 'achieve', 'get', 'gain'],
        'significant improvements': ['better results', 'major gains', 'notable progress', 'substantial progress'],
        'productivity metrics': ['performance measures', 'efficiency indicators', 'output measures', 'results'],
        'performance indicators': ['metrics', 'measurements', 'benchmarks', 'results'],
        'furthermore': ['also', 'additionally', 'moreover', 'plus'],
        'facilitates': ['enables', 'helps', 'supports', 'allows'],
        'decision-making processes': ['decision making', 'choices', 'business decisions', 'strategic choices'],
        'advanced': ['sophisticated', 'cutting-edge', 'modern', 'state-of-the-art'],
        'data analytics': ['data analysis', 'information processing', 'data insights', 'analytics'],
        'predictive modeling': ['forecasting', 'prediction tools', 'modeling', 'trend analysis'],
        'capabilities': ['abilities', 'features', 'functions', 'tools'],
        'sophisticated systems': ['complex systems', 'advanced tools', 'smart systems', 'modern platforms'],
        'enable': ['allow', 'help', 'let', 'make possible'],
        'leverage': ['use', 'utilize', 'take advantage of', 'harness'],
        'vast amounts': ['large amounts', 'huge volumes', 'massive quantities', 'tons'],
        'generate': ['create', 'produce', 'develop', 'make'],
        'actionable insights': ['useful information', 'practical findings', 'valuable data', 'key insights'],
        'business outcomes': ['results', 'business results', 'performance', 'success'],

        // Common AI words
        'very': ['quite', 'rather', 'pretty', 'fairly', 'really'],
        'also': ['additionally', 'furthermore', 'moreover', 'as well', 'too'],
        'but': ['however', 'yet', 'although', 'though', 'still'],
        'because': ['since', 'as', 'due to the fact that', 'given that'],
        'shows': ['demonstrates', 'indicates', 'reveals', 'suggests', 'proves'],
        'important': ['significant', 'crucial', 'vital', 'essential', 'key'],
        'different': ['various', 'distinct', 'diverse', 'alternative', 'separate'],
        'good': ['effective', 'beneficial', 'valuable', 'positive', 'excellent'],
        'bad': ['negative', 'detrimental', 'harmful', 'problematic', 'poor'],
        'big': ['large', 'substantial', 'significant', 'considerable', 'major'],
        'small': ['minor', 'limited', 'modest', 'minimal', 'tiny']
    };

    let result = sentence;

    // Apply aggressive replacements with higher probability for AI-typical phrases
    for (const [original, alternatives] of Object.entries(aggressiveVariations)) {
        // Higher replacement probability for longer, more AI-typical phrases
        const replacementProbability = original.includes(' ') ? 0.8 : 0.6;

        if (Math.random() < replacementProbability) {
            const regex = new RegExp(`\\b${original.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
            const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
            result = result.replace(regex, replacement);
        }
    }

    return result;
}

/**
 * Aggressive structural changes for AI detection bypass
 */
function applyStructuralVariations(sentence) {
    let result = sentence;

    // Aggressive sentence pattern restructuring

    // 1. Convert passive to active voice and vice versa
    if (Math.random() < 0.4) {
        // "AI is used by companies" → "Companies use AI"
        result = result.replace(/(\w+)\s+is\s+(\w+ed)\s+by\s+(\w+)/gi, '$3 $2 $1');

        // "Companies implement AI" → "AI is implemented by companies" (sometimes reverse)
        if (Math.random() < 0.3) {
            result = result.replace(/(\w+)\s+(implement|use|deploy|adopt)\s+(\w+)/gi, '$3 is $2ed by $1');
        }
    }

    // 2. Restructure "X enables Y" patterns
    if (Math.random() < 0.5) {
        result = result.replace(/(\w+)\s+enables?\s+(\w+)\s+to\s+(\w+)/gi, (_match, subject, object, verb) => {
            const alternatives = [
                `${object} can ${verb} through ${subject}`,
                `With ${subject}, ${object} can ${verb}`,
                `${subject} allows ${object} to ${verb}`,
                `${object} ${verb} using ${subject}`
            ];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });
    }

    // 3. Break down complex sentences with multiple clauses
    if (Math.random() < 0.3 && sentence.includes(',') && sentence.length > 80) {
        // Split at logical break points
        result = result.replace(/,\s+(which|that|where|when)\s+/gi, '. This ');
        result = result.replace(/,\s+(however|furthermore|moreover|additionally)\s+/gi, '. $1, ');
    }

    // 4. Restructure "through X" patterns
    if (Math.random() < 0.4) {
        result = result.replace(/through\s+(\w+(?:\s+\w+)*)/gi, (_match, method) => {
            const alternatives = [
                `using ${method}`,
                `via ${method}`,
                `by means of ${method}`,
                `with ${method}`
            ];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });
    }

    // 5. Vary sentence starters
    if (Math.random() < 0.3) {
        // "Organizations can..." → "Companies are able to..."
        result = result.replace(/^Organizations\s+can\s+/i, () => {
            const alternatives = ['Companies are able to ', 'Businesses can ', 'Firms have the ability to '];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });

        // "The implementation of..." → "Implementing..."
        result = result.replace(/^The\s+implementation\s+of\s+/i, 'Implementing ');

        // "Furthermore," → various alternatives
        result = result.replace(/^Furthermore,\s*/i, () => {
            const alternatives = ['Also, ', 'Additionally, ', 'Moreover, ', 'Plus, ', 'On top of that, '];
            return alternatives[Math.floor(Math.random() * alternatives.length)];
        });
    }

    // 6. Add natural human hesitation/thinking patterns
    if (Math.random() < 0.2 && sentence.length > 50) {
        const hesitations = [
            ', you know,',
            ', I mean,',
            ', basically,',
            ', essentially,',
            ', in other words,'
        ];
        const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];

        // Insert at natural break points
        if (sentence.includes(',')) {
            result = result.replace(/,\s/, hesitation + ' ');
        }
    }

    return result;
}

/**
 * Calculate hesitation probability based on sentence context
 */
function calculateHesitationProbability(sentence, baseFrequency) {
    let probability = baseFrequency;

    // Reduce probability for certain contexts
    if (sentence.length < 10) probability *= 0.2;          // Short sentences
    if (isQuestionOrExclamation(sentence)) probability *= 0.3;
    if (containsNumbers(sentence)) probability *= 0.4;
    if (isFormalTone(sentence)) probability *= 0.5;
    if (isListItem(sentence)) probability = 0;        // Never add to lists

    // Increase probability for longer, complex sentences
    if (sentence.length > 50) probability *= 1.2;
    if (hasComplexStructure(sentence)) probability *= 1.1;

    return Math.min(probability, 0.05); // Cap at 5%
}

/**
 * Add contextual hesitation based on sentence type
 */
function addContextualHesitation(sentence, contextualAlternatives, hesitationMarkers) {
    // Choose appropriate hesitation based on context
    let hesitationType = 'transition';

    if (isEmphatic(sentence)) hesitationType = 'emphasis';
    if (needsClarification(sentence)) hesitationType = 'clarification';

    const markers = contextualAlternatives[hesitationType] || hesitationMarkers;
    const marker = markers[Math.floor(Math.random() * markers.length)];

    return `${marker} ${sentence.charAt(0).toLowerCase()}${sentence.slice(1)}`;
}

/**
 * Helper functions for sentence analysis
 */
function isQuestionOrExclamation(sentence) {
    return /[?!]/.test(sentence);
}

function containsNumbers(sentence) {
    return /\d+/.test(sentence);
}

function isFormalTone(sentence) {
    const formalIndicators = [
        /\btherefore\b/i, /\bfurthermore\b/i, /\bmoreover\b/i,
        /\bconsequently\b/i, /\bnevertheless\b/i, /\bhowever\b/i,
        /\bin conclusion\b/i, /\bin summary\b/i
    ];

    return formalIndicators.some(pattern => pattern.test(sentence));
}

function isListItem(sentence) {
    return /^\s*[-•*]/.test(sentence) || /^\s*\d+\./.test(sentence);
}

function hasComplexStructure(sentence) {
    const commaCount = (sentence.match(/,/g) || []).length;
    const clauseMarkers = (sentence.match(/\b(which|that|where|when|while|although|because|since|if|unless)\b/gi) || []).length;

    return commaCount >= 2 || clauseMarkers >= 1;
}

function isEmphatic(sentence) {
    const emphaticPatterns = [
        /\b(very|extremely|incredibly|absolutely|definitely|certainly)\b/i,
        /\b(crucial|essential|vital|important|significant)\b/i,
        /!/
    ];

    return emphaticPatterns.some(pattern => pattern.test(sentence));
}

function needsClarification(sentence) {
    const clarificationPatterns = [
        /\b(means|refers to|indicates|suggests|implies)\b/i,
        /\b(in other words|that is|namely|specifically)\b/i
    ];

    return clarificationPatterns.some(pattern => pattern.test(sentence));
}

/**
 * Enhanced protection patterns for inappropriate hesitation placement
 */
function isInappropriateForHesitation(sentence) {
    const trimmed = sentence.trim();

    // Protect very short sentences (likely headings or labels)
    if (trimmed.length < 10) return true;

    // Enhanced protection patterns
    const protectedPatterns = [
        /^[IVX]+\.\s/,                    // Roman numerals (I., II., III.)
        /^[A-Z]\.\s/,                     // Letter headings (A., B., C.)
        /^\d+\.\s/,                       // Number headings (1., 2., 3.)
        /^#+\s/,                          // Markdown headers
        /^[A-Z][^:]*:/,                   // Title-like patterns
        /^\s*[-•*]\s/,                    // Bullet points
        /^\s*\([a-z]\)/,                  // Lettered lists (a), (b), (c)
        /^\s*\(\d+\)/,                    // Numbered lists (1), (2), (3)
        /^[A-Z\s]+:$/,                    // ALL CAPS headings
        /^(Introduction|Conclusion|Summary|Abstract|Overview):/i,
        /^(Hook|Thesis|Topic|Executive Summary|Key Findings):/i,
        /^(Step|Phase|Part)\s+\d+/i,     // Step/Phase markers
        /:\s*$/,                          // Lines ending with colon
        /^-\s/                            // Dash lists
    ];

    // Check against protected patterns
    for (const pattern of protectedPatterns) {
        if (pattern.test(trimmed)) {
            return true;
        }
    }

    // Additional checks for common document structures
    if (trimmed.length === 0) return true; // Empty lines
    if (trimmed.length < 3) return true;   // Very short lines
    if (isHeading(trimmed)) return true;
    if (isTechnicalTerm(trimmed)) return true;

    return false;
}

/**
 * Check if line looks like a heading
 */
function isHeading(line) {
    const headingIndicators = [
        /^[A-Z][^.!?]*$/,                 // All caps or title case, no punctuation
        /^\d+\.\d+/,                      // Section numbers like 1.1, 2.3
        /^Chapter \d+/i,                  // Chapter headings
        /^Section [A-Z]/i,                // Section headings
        /^Part [IVX]+/i,                  // Part headings
        /^[A-Z\s]+$/                      // All caps
    ];

    return headingIndicators.some(pattern => pattern.test(line.trim()));
}

/**
 * Check if line contains technical terms or proper nouns
 */
function isTechnicalTerm(line) {
    const technicalPatterns = [
        /\b[A-Z]{2,}\b/,                  // Acronyms
        /\b\d+(\.\d+)*\b/,                // Version numbers
        /\b[A-Z][a-z]+[A-Z][a-z]*\b/,    // CamelCase
        /\b\w+\.\w+\b/,                   // Dotted notation
        /\b(API|URL|HTTP|HTML|CSS|JS|JSON|XML|SQL)\b/i
    ];

    return technicalPatterns.some(pattern => pattern.test(line));
}

/**
 * Detect high AI patterns and adjust aggressiveness accordingly
 */
function detectAndAdjustAggressiveness(text, baseAggressiveness) {
    let aiScore = 0;
    const aiPatterns = [
        // Formal AI language patterns
        /artificial intelligence/gi,
        /transformative technology/gi,
        /fundamentally revolutionizes/gi,
        /operational efficiency/gi,
        /competitive advantages/gi,
        /comprehensive strategic planning/gi,
        /substantial financial investment/gi,
        /numerous studies demonstrate/gi,
        /significant improvements/gi,
        /productivity metrics/gi,
        /performance indicators/gi,
        /facilitates enhanced/gi,
        /decision-making processes/gi,
        /advanced data analytics/gi,
        /predictive modeling capabilities/gi,
        /sophisticated systems/gi,
        /actionable insights/gi,
        /business outcomes/gi,

        // AI sentence patterns
        /\w+ represents a \w+ that/gi,
        /organizations across various industries/gi,
        /increasingly implementing/gi,
        /requires comprehensive/gi,
        /however, numerous/gi,
        /furthermore, \w+ facilitates/gi,
        /these sophisticated/gi,
        /enable organizations to/gi,
        /leverage vast amounts/gi,

        // Repetitive formal structures
        /\w+ systems? \w+ \w+ to \w+/gi,
        /the implementation of \w+ requires/gi,
        /companies utilizing \w+ experience/gi
    ];

    // Count AI pattern matches
    aiPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
            aiScore += matches.length;
        }
    });

    // Calculate AI density
    const wordCount = text.split(/\s+/).length;
    const aiDensity = aiScore / wordCount;

    // Adjust aggressiveness based on AI density
    let adjustedAggressiveness = baseAggressiveness;

    if (aiDensity > 0.1) { // High AI density
        adjustedAggressiveness = Math.min(0.95, baseAggressiveness + 0.3);
    } else if (aiDensity > 0.05) { // Medium AI density
        adjustedAggressiveness = Math.min(0.9, baseAggressiveness + 0.2);
    } else if (aiDensity > 0.02) { // Low AI density
        adjustedAggressiveness = Math.min(0.85, baseAggressiveness + 0.1);
    }

    console.log(`AI Detection: ${aiScore} patterns, density: ${(aiDensity * 100).toFixed(2)}%, aggressiveness: ${baseAggressiveness} → ${adjustedAggressiveness.toFixed(2)}`);

    return adjustedAggressiveness;
}

/**
 * Preserve paragraph structure when processing formatted content aggressively
 */
function preserveParagraphStructure(originalText, processedText) {
    const originalParagraphs = originalText.split('\n\n');
    const processedSentences = processedText.split(/(?<=[.!?])\s+/);

    if (originalParagraphs.length <= 1) {
        return processedText; // No paragraph structure to preserve
    }

    // Reconstruct paragraphs by distributing processed sentences
    const result = [];
    let sentenceIndex = 0;

    for (let i = 0; i < originalParagraphs.length; i++) {
        const originalParagraph = originalParagraphs[i].trim();
        if (!originalParagraph) continue;

        // Count sentences in original paragraph
        const originalSentences = originalParagraph.split(/(?<=[.!?])\s+/);
        const sentenceCount = originalSentences.length;

        // Take corresponding processed sentences
        const paragraphSentences = processedSentences.slice(sentenceIndex, sentenceIndex + sentenceCount);
        sentenceIndex += sentenceCount;

        // Join sentences for this paragraph
        result.push(paragraphSentences.join(' '));
    }

    return result.join('\n\n');
}

/**
 * Adds conversational elements - with context awareness
 */
function addConversationalElements(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const processedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        let processedSentence = sentence;

        // Very conservative conversational markers (max 2% chance)
        if (Math.random() < aggressiveness * 0.02 && sentence.length > 30) {
            const conversationalMarkers = [
                { pattern: /\b(This|That)\s+(is|was)\s+/gi, replacements: ['$1 $2 basically ', '$1 $2 essentially '] }
            ];

            conversationalMarkers.forEach(marker => {
                processedSentence = processedSentence.replace(marker.pattern, (_match, ...groups) => {
                    const replacement = marker.replacements[Math.floor(Math.random() * marker.replacements.length)];
                    return replacement.replace(/\$(\d+)/g, (_, num) => groups[parseInt(num) - 1] || '');
                });
            });
        }

        return processedSentence;
    });

    return processedSentences.join(' ');
}

/**
 * Adds human-like tangents and asides - very conservative
 */
function addHumanTangents(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const tangentMarkers = [
        ' (by the way)',
        ' (worth noting)'
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very low frequency for tangents (max 1% chance)
        if (Math.random() < aggressiveness * 0.01 && sentence.length > 50) {
            const tangent = tangentMarkers[Math.floor(Math.random() * tangentMarkers.length)];
            const insertPoint = Math.floor(sentence.length * 0.6);
            return sentence.slice(0, insertPoint) + tangent + sentence.slice(insertPoint);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds personal perspective markers - conservative approach
 */
function addPersonalPerspective(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const perspectiveMarkers = [
        'I think ',
        'It seems ',
        'Personally, '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very conservative frequency (max 2% chance)
        if (Math.random() < aggressiveness * 0.02 && sentence.length > 30 && index > 0) {
            const marker = perspectiveMarkers[Math.floor(Math.random() * perspectiveMarkers.length)];
            return marker + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Subtle coherence disruption - breaks AI's perfect logical flow
 */
function subtleCoherenceDisruption(text, aggressiveness, protectedSentences = []) {
    let result = text;

    // 1. Occasionally break logical transitions
    result = breakLogicalTransitions(result, aggressiveness, protectedSentences);

    // 2. Add human-like topic drift (very conservative)
    result = addTopicDrift(result, aggressiveness * 0.2, protectedSentences);

    // 3. Insert human-like contradictions and corrections (very conservative)
    result = addHumanContradictions(result, aggressiveness * 0.1, protectedSentences);

    return result;
}

/**
 * Breaks overly logical transitions
 */
function breakLogicalTransitions(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const logicalConnectors = [
        { pattern: /\bTherefore,\s*/gi, replacements: ['So, ', 'Thus, '] },
        { pattern: /\bConsequently,\s*/gi, replacements: ['So, ', 'Then, '] },
        { pattern: /\bAs a result,\s*/gi, replacements: ['So, ', 'Because of this, '] }
    ];

    logicalConnectors.forEach(connector => {
        result = result.replace(connector.pattern, (match, offset) => {
            // Check if this match is in a protected sentence
            const beforeMatch = text.substring(0, offset);
            const sentenceIndex = (beforeMatch.match(/[.!?]/g) || []).length;

            if (protectedSentences.includes(sentenceIndex)) {
                return match;
            }

            if (Math.random() < aggressiveness * 0.2) { // Reduced frequency
                const replacement = connector.replacements[Math.floor(Math.random() * connector.replacements.length)];
                return replacement;
            }
            return match;
        });
    });

    return result;
}

/**
 * Adds subtle topic drift - very conservative
 */
function addTopicDrift(text, aggressiveness, protectedSentences = []) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);

    const driftMarkers = [
        'On a related note, '
    ];

    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Very low frequency for topic drift (max 0.5% chance)
        if (Math.random() < aggressiveness * 0.005 && index > 2 && sentence.length > 30) {
            const drift = driftMarkers[Math.floor(Math.random() * driftMarkers.length)];
            return drift + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Adds human-like contradictions and corrections - very conservative
 */
function addHumanContradictions(text, aggressiveness, protectedSentences = []) {
    let result = text;

    const contradictionPatterns = [
        'Let me rephrase that - '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const enhancedSentences = sentences.map((sentence, index) => {
        // Skip protected sentences
        if (protectedSentences.includes(index)) {
            return sentence;
        }

        // Skip inappropriate content
        if (isInappropriateForHesitation(sentence)) {
            return sentence;
        }

        // Extremely low frequency for contradictions (max 0.2% chance)
        if (Math.random() < aggressiveness * 0.002 && index > 1 && sentence.length > 40) {
            const contradiction = contradictionPatterns[Math.floor(Math.random() * contradictionPatterns.length)];
            return contradiction + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    return enhancedSentences.join(' ');
}

/**
 * Final polish and quality check
 */
function finalPolishAndQualityCheck(text, _maintainTone, documentAnalysis) {
    let result = text;

    // 1. Clean up any formatting issues (preserve structure for formal docs)
    result = cleanupFormatting(result, documentAnalysis);

    // 2. Ensure readability is maintained (skip for formatted documents)
    if (!documentAnalysis.preserveFormatting) {
        result = ensureReadability(result);
    }

    // 3. Final human touch additions (very conservative for formal docs)
    if (!documentAnalysis.hasFormalStructure) {
        result = addFinalHumanTouches(result);
    }

    // 4. Quality validation with enhanced checks
    const quality = validateQuality(result, documentAnalysis);
    if (!quality.isAcceptable) {
        console.warn('Quality issues detected:', quality.issues);
        // Apply conservative fixes if quality is poor
        result = applyConservativeFixes(result, documentAnalysis);
    }

    return result;
}

/**
 * Cleans up formatting issues while preserving document structure
 */
function cleanupFormatting(text, documentAnalysis) {
    if (documentAnalysis && documentAnalysis.preserveFormatting) {
        // For formal documents, preserve line breaks and only clean up basic issues
        return text
            .replace(/[ \t]+/g, ' ') // Multiple spaces/tabs to single space (but preserve newlines)
            .replace(/[ \t]+([.!?])/g, '$1') // Remove space before punctuation (but not newlines)
            .replace(/([.!?])[ \t]*([.!?])/g, '$1 $2') // Fix multiple punctuation (but not newlines)
            .replace(/[ \t]+,/g, ',') // Remove space before comma (but not newlines)
            .replace(/,([^\s])/g, ', $1') // Add space after comma
            .replace(/^[ \t]+|[ \t]+$/gm, ''); // Trim each line but preserve line breaks
    } else {
        // For regular text, standard cleanup
        return text
            .replace(/\s+/g, ' ') // Multiple spaces to single
            .replace(/\s+([.!?])/g, '$1') // Remove space before punctuation
            .replace(/([.!?])\s*([.!?])/g, '$1 ') // Fix multiple punctuation
            .replace(/\s+,/g, ',') // Remove space before comma
            .replace(/,([^\s])/g, ', $1') // Add space after comma
            .trim();
    }
}

/**
 * Ensures readability is maintained
 */
function ensureReadability(text) {
    let result = text;

    // Check for overly long sentences and break them
    const sentences = result.split(/(?<=[.!?])\s+/);
    const readableSentences = sentences.map(sentence => {
        if (sentence.length > 150) {
            // Find a good breaking point
            const breakPoints = [', and ', ', but ', ', so ', '; '];
            for (const breakPoint of breakPoints) {
                const index = sentence.indexOf(breakPoint, 60);
                if (index > 0 && index < sentence.length - 30) {
                    const firstPart = sentence.substring(0, index).trim() + '.';
                    const secondPart = sentence.substring(index + breakPoint.length).trim();
                    return firstPart + ' ' + secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
                }
            }
        }
        return sentence;
    });

    return readableSentences.join(' ');
}

/**
 * Adds final human touches
 */
function addFinalHumanTouches(text) {
    let result = text;

    // Add occasional emphasis
    result = result.replace(/\b(really|very|quite)\s+(\w+)/gi, (match, intensifier, word) => {
        if (Math.random() > 0.9) {
            return `${intensifier}, ${intensifier} ${word}`;
        }
        return match;
    });

    // Add occasional informal contractions
    const informalContractions = {
        'going to': "gonna",
        'want to': "wanna",
        'have to': "gotta",
        'out of': "outta"
    };

    Object.entries(informalContractions).forEach(([formal, informal]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.95) { // Very rarely
                return preserveCase(match, informal);
            }
            return match;
        });
    });

    return result;
}

/**
 * Validates text quality with enhanced checks
 */
function validateQuality(text, documentAnalysis) {
    const issues = [];

    // Check for basic grammar issues
    if (text.includes('..')) issues.push('Multiple periods found');
    if (text.includes('  ') && !documentAnalysis?.preserveFormatting) issues.push('Multiple spaces found');
    if (text.match(/[a-z]\.[A-Z]/)) issues.push('Missing space after period');

    // Check for readability
    const sentences = text.split(/(?<=[.!?])\s+/);
    const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    if (avgLength > 120) issues.push('Average sentence length too high');

    // Check for excessive hesitation markers
    const hesitationMarkers = ['well,', 'so,', 'actually,', 'listen,', 'look,'];
    let hesitationCount = 0;
    hesitationMarkers.forEach(marker => {
        hesitationCount += (text.toLowerCase().match(new RegExp(`\\b${marker}`, 'g')) || []).length;
    });

    // More strict limits for hesitation markers
    const maxHesitationRatio = documentAnalysis?.hasFormalStructure ? 0.02 : 0.05; // 2% for formal, 5% for casual
    if (hesitationCount > sentences.length * maxHesitationRatio) {
        issues.push('Too many hesitation markers');
    }

    // Check for inappropriate hesitation in formal content
    if (documentAnalysis?.hasFormalStructure) {
        const inappropriateHesitations = text.match(/\b(listen|look),\s*[a-z]/gi);
        if (inappropriateHesitations && inappropriateHesitations.length > 0) {
            issues.push('Inappropriate hesitation markers in formal content');
        }
    }

    return {
        isAcceptable: issues.length === 0,
        issues: issues
    };
}

/**
 * Applies conservative fixes if quality is poor
 */
function applyConservativeFixes(text, documentAnalysis) {
    let result = text;

    // Remove excessive hesitation markers
    const excessiveHesitations = ['\\blisten,\\s*', '\\blook,\\s*', '\\bwell,\\s*', '\\bso,\\s*'];
    excessiveHesitations.forEach(pattern => {
        result = result.replace(new RegExp(pattern, 'gi'), '');
    });

    // Remove parenthetical remarks if too many
    const parentheticalCount = (result.match(/\([^)]*\)/g) || []).length;
    const sentences = result.split(/(?<=[.!?])\s+/);
    if (parentheticalCount > sentences.length * 0.1) {
        result = result.replace(/\s*\([^)]*\)\s*/g, ' ');
    }

    // Clean up spaces appropriately
    if (documentAnalysis?.preserveFormatting) {
        result = result.replace(/[ \t]+/g, ' '); // Preserve line breaks
    } else {
        result = result.replace(/\s+/g, ' ').trim(); // Standard cleanup
    }

    return result;
}
